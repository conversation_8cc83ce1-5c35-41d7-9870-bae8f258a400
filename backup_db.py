import os
import shutil
from datetime import datetime

# List of database files to backup
DB_FILES = [
    os.path.join('instance', 'app_store.db'),
    os.path.join('app', 'fingerprints.db'),
]

BACKUP_FOLDER = 'db_backups'

def backup_databases():
    os.makedirs(BACKUP_FOLDER, exist_ok=True)
    for db_file in DB_FILES:
        if os.path.exists(db_file):
            base = os.path.basename(db_file)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{base}.{timestamp}.bak"
            backup_path = os.path.join(BACKUP_FOLDER, backup_name)
            shutil.copy2(db_file, backup_path)
            print(f"Backed up {db_file} to {backup_path}")
        else:
            print(f"Database file not found: {db_file}")

if __name__ == "__main__":
    backup_databases()
