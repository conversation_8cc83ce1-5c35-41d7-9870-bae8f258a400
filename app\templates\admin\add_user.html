{% extends "base.html" %}

{% block title %}Add New User - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-person-plus"></i> Add New User</h1>
        <a href="{{ url_for('admin_users') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Create User Account</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="addUserForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   minlength="3" maxlength="50" required>
                            <div class="form-text">At least 3 characters, letters and numbers only</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">User's email address for notifications</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Account Role *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select role</option>
                                <option value="publisher">Publisher - Can upload and manage their own apps</option>
                                <option value="admin">Administrator - Full system access</option>
                            </select>
                            <div class="form-text">
                                <strong>Publisher:</strong> Limited to managing their own apps<br>
                                <strong>Admin:</strong> Can manage all apps and users
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   minlength="6" required>
                            <div class="form-text">At least 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   minlength="6" required>
                            <div class="invalid-feedback" id="password-mismatch" style="display: none;">
                                Passwords do not match
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Security Note:</strong> Only administrators can create new user accounts. 
                            The new user will receive their login credentials and can change their password after first login.
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-person-plus"></i> Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Form validation
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const passwordMismatch = document.getElementById('password-mismatch');
    
    if (password !== confirmPassword) {
        e.preventDefault();
        passwordMismatch.style.display = 'block';
        document.getElementById('confirm_password').classList.add('is-invalid');
        return false;
    } else {
        passwordMismatch.style.display = 'none';
        document.getElementById('confirm_password').classList.remove('is-invalid');
    }
});

// Real-time password matching
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const passwordMismatch = document.getElementById('password-mismatch');
    
    if (confirmPassword && password !== confirmPassword) {
        passwordMismatch.style.display = 'block';
        this.classList.add('is-invalid');
    } else {
        passwordMismatch.style.display = 'none';
        this.classList.remove('is-invalid');
    }
});

// Username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const regex = /^[a-zA-Z0-9_]+$/;
    
    if (username && !regex.test(username)) {
        this.setCustomValidity('Username can only contain letters, numbers, and underscores');
    } else {
        this.setCustomValidity('');
    }
});

// Role selection helper
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const helpText = this.nextElementSibling;
    
    if (role === 'admin') {
        helpText.innerHTML = '<strong class="text-warning">⚠️ Admin:</strong> Full system access - can manage all apps and users';
    } else if (role === 'publisher') {
        helpText.innerHTML = '<strong class="text-info">📝 Publisher:</strong> Limited to managing their own apps';
    }
});
</script>
{% endblock %}
