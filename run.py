#!/usr/bin/env python3
"""
App Store - Run Script
This script starts the Flask application with proper configuration.
"""

import os
import sys
from app import app
from app.config import Config

def main():
    """Main function to run the Flask application"""
    
    if not os.environ.get('SECRET_KEY'):
        os.environ['SECRET_KEY'] = Config.SECRET_KEY
    
    # Create necessary directories
    # directories = [
    #     'uploads',
    #     'uploads/apps',
    #     'uploads/screenshots', 
    #     'logs',
    #     'static',
    #     'static/css',
    #     'static/js'
    # ]
    
    # for directory in directories:
    #     os.makedirs(directory, exist_ok=True)
    #     print(f"✓ Directory '{directory}' ready")
    
    # Auto-setup SQLite DBs if not exist
    db_path = os.path.join('app', 'instance', 'app_store.db')
    if not os.path.exists(db_path):
        print(f"✓ Creating database at {db_path}")
        from app import app
        from app.models import db
        with app.app_context():
            db.create_all()

    fp_db_path = os.path.join('app', 'fingerprints.db')
    if not os.path.exists(fp_db_path):
        print(f"✓ Creating fingerprints DB at {fp_db_path}")
        import sqlite3
        with sqlite3.connect(fp_db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fingerprints (
                    uuid TEXT PRIMARY KEY,
                    fingerprint TEXT UNIQUE,
                    hmac_key TEXT,
                    browser_data TEXT
                )
            ''')

    # Check if running in development or production
    debug_mode = os.environ.get('FLASK_ENV') == 'development' or '--debug' in sys.argv
    
    print("\n" + "="*50)
    print("🏪 APP STORE - Starting Server")
    print("="*50)
    print(f"Debug Mode: {'ON' if debug_mode else 'OFF'}")
    print(f"Host: 0.0.0.0")
    print(f"Port: 5000")
    print(f"URL: http://localhost:5000")
    print("\nAdmin Credentials:")
    print(f"Username: {app.config['ADMIN_USERNAME']}")
    print(f"Password: {app.config['ADMIN_PASSWORD']}")
    print("="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")
    
    try:
        # Run the Flask application
        app.run(
            debug=debug_mode,
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n" + "="*50)
        print("🛑 Server stopped by user")
        print("="*50)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
