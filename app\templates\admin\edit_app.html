{% extends "base.html" %}

{% block title %}Edit {{ app.name }} - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-pencil"></i> Edit App: {{ app.name }}</h1>
        <div>
            <a href="{{ url_for('app_detail', app_id=app.id) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye"></i> View App
            </a>
            <a href="{{ url_for('admin_apps') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Apps
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <!-- Basic Information -->
                        <h5 class="mb-3">Basic Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">App Name *</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ app.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="developer" class="form-label">Developer *</label>
                                <input type="text" class="form-control" id="developer" name="developer"
                                       value="{{ app.developer }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description *</label>
                            <input type="text" class="form-control" id="short_description" name="short_description"
                                   maxlength="200" value="{{ app.short_description }}" required>
                            <div class="form-text">Brief description shown in app listings (max 200 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="6" required>{{ app.description }}</textarea>
                            <div class="form-text">Detailed description shown on app page</div>
                        </div>

                        <!-- App Details -->
                        <h5 class="mb-3 mt-4">App Details</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="version" class="form-label">Version *</label>
                                <input type="text" class="form-control" id="version" name="version"
                                       value="{{ app.version }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category }}" {% if app.category == category %}selected{% endif %}>
                                        {{ category }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="price" name="price"
                                       min="0" step="0.01" value="{{ app.price }}">
                                <div class="form-text">Set to 0 for free apps</div>
                            </div>
                        </div>

                        <!-- Options -->
                        <h5 class="mb-3 mt-4">Options</h5>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                       {% if app.is_featured %}checked{% endif %}>
                                <label class="form-check-label" for="is_featured">
                                    Featured App
                                </label>
                                <div class="form-text">Featured apps appear in the featured section</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update App
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- App Info Sidebar -->
        <div class="col-lg-4">
            <!-- Current App Info -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Current App Info</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if app.icon_path %}
                        <img src="{{ url_for('uploaded_file', filename=app.icon_path.replace('uploads/', '')) }}"
                             class="img-fluid rounded" style="max-width: 128px; max-height: 128px;" alt="{{ app.name }}">
                        {% else %}
                        <div class="bg-light border rounded d-flex align-items-center justify-content-center"
                             style="width: 128px; height: 128px; margin: 0 auto;">
                            <i class="bi bi-app display-4 text-muted"></i>
                        </div>
                        {% endif %}
                    </div>

                    <table class="table table-sm">
                        <tr>
                            <td><strong>File Size:</strong></td>
                            <td>{{ app.get_file_size_formatted() }}</td>
                        </tr>
                        <tr>
                            <td><strong>Downloads:</strong></td>
                            <td>{{ app.downloads }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ app.created_at.strftime('%m/%d/%Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ app.updated_at.strftime('%m/%d/%Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Screenshots:</strong></td>
                            <td>{{ app.screenshots|length }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- File Management -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-files"></i> File Management</h5>
                </div>
                <div class="card-body">
                    <p class="small text-muted">
                        To update app files, icon, or screenshots, you'll need to delete and re-add the app.
                        This ensures file integrity and proper cleanup.
                    </p>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('download_app', app_id=app.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-download"></i> Download Current File
                        </a>
                        <a href="{{ url_for('app_detail', app_id=app.id) }}" class="btn btn-sm btn-outline-info">
                            <i class="bi bi-eye"></i> View Public Page
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ app.downloads }}</h4>
                                <small class="text-muted">Downloads</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">
                                {% if app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(app.price) }}{% endif %}
                            </h4>
                            <small class="text-muted">Price</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-3 border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Danger Zone</h5>
                </div>
                <div class="card-body">
                    <p class="small text-muted">
                        Permanently delete this app and all associated files. This action cannot be undone.
                    </p>
                    <button type="button" class="btn btn-danger btn-sm w-100"
                            onclick="confirmDelete({{ app.id }}, '{{ app.name }}')">
                        <i class="bi bi-trash"></i> Delete App
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the app "<span id="deleteAppName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(appId, appName) {
    document.getElementById('deleteAppName').textContent = appName;
    document.getElementById('deleteForm').action = `/admin/apps/delete/${appId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Character counter for short description
document.getElementById('short_description').addEventListener('input', function(e) {
    const maxLength = 200;
    const currentLength = e.target.value.length;
    const remaining = maxLength - currentLength;

    let helpText = e.target.nextElementSibling;
    helpText.textContent = `Brief description shown in app listings (${remaining} characters remaining)`;

    if (remaining < 20) {
        helpText.classList.add('text-warning');
    } else {
        helpText.classList.remove('text-warning');
    }
});
</script>
{% endblock %}
