{% extends "base.html" %}

{% block title %}Manage Users - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-people"></i> Manage Users</h1>
        <div>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('admin_add_user') }}" class="btn btn-success">
                <i class="bi bi-person-plus"></i> Add New User
            </a>
        </div>
    </div>

    {% if users.items %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Apps Published</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr>
                            <td>
                                <strong>{{ user.username }}</strong>
                                {% if user.id == session.user_id %}
                                <span class="badge bg-info ms-2">You</span>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.role == 'admin' %}
                                <span class="badge bg-danger">
                                    <i class="bi bi-shield-fill"></i> Admin
                                </span>
                                {% else %}
                                <span class="badge bg-primary">
                                    <i class="bi bi-person"></i> Publisher
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                {% set user_apps = user.apps %}
                                {{ user_apps|length }} apps
                                {% if user_apps|length > 0 %}
                                <br><small class="text-muted">
                                    {{ user_apps|sum(attribute='downloads') }} total downloads
                                </small>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%m/%d/%Y') }}</td>
                            <td>
                                {% if user.last_login %}
                                {{ user.last_login.strftime('%m/%d/%Y') }}
                                {% else %}
                                <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if user.id != session.user_id %}
                                    <button class="btn btn-sm btn-outline-warning" 
                                            onclick="editUser({{ user.id }}, '{{ user.username }}', '{{ user.email }}', '{{ user.role }}')"
                                            title="Edit User">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete({{ user.id }}, '{{ user.username }}')"
                                            title="Delete User">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% else %}
                                    <span class="text-muted small">Current User</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if users.pages > 1 %}
    <nav aria-label="User pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if users.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_users', page=users.prev_num) }}">Previous</a>
            </li>
            {% endif %}
            
            {% for page_num in users.iter_pages() %}
                {% if page_num %}
                    {% if page_num != users.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_users', page=page_num) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if users.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_users', page=users.next_num) }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h3 class="mt-3">No Users Found</h3>
            <p class="text-muted">Start by creating the first user account.</p>
            <a href="{{ url_for('admin_add_user') }}" class="btn btn-success">
                <i class="bi bi-person-plus"></i> Add First User
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone. All apps published by this user will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Role</label>
                        <select class="form-select" id="editRole" name="role" required>
                            <option value="publisher">Publisher</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function confirmDelete(userId, username) {
    document.getElementById('deleteUserName').textContent = username;
    document.getElementById('deleteForm').action = `/admin/users/delete/${userId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function editUser(userId, username, email, role) {
    document.getElementById('editUsername').value = username;
    document.getElementById('editEmail').value = email;
    document.getElementById('editRole').value = role;
    document.getElementById('editPassword').value = '';
    document.getElementById('editForm').action = `/admin/users/edit/${userId}`;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}
</script>
{% endblock %}
