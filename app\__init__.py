import os
from flask import Flask
from app.config import Config
from app.models import db
import logging

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)

db.init_app(app)
Config.init_app(app)

# Setup logging
if not app.config['LOG_FOLDER']:
    app.config['LOG_FOLDER'] = 'logs'
if not app.config['LOG_FILE']:
    app.config['LOG_FILE'] = 'app.log'
log_file = os.path.join(app.config['LOG_FOLDER'], app.config['LOG_FILE'])
if not os.path.exists(app.config['LOG_FOLDER']):
    os.makedirs(app.config['LOG_FOLDER'])
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

# Import and register routes
from app.views.routes import *
from app.api.routes import *
from app.views.routes import views
app.register_blueprint(views)
