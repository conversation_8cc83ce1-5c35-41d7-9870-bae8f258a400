#!/usr/bin/env python3
"""
App Store - Setup Script
This script sets up the application environment and initializes the database.
"""

import os
import sys
import subprocess
from app import app, db
from app.models import App, Screenshot, DownloadLog, AdminLog

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")

    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")

    directories = [
        'uploads',
        'uploads/apps',
        'uploads/screenshots',
        'logs',
        'static',
        'static/css',
        'static/js',
        'templates',
        'templates/admin'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

    print("✅ All directories created")

def initialize_database():
    """Initialize the database with tables"""
    print("🗄️  Initializing database...")

    try:
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully")

            # Check if we have any apps
            app_count = App.query.count()
            print(f"📊 Current apps in database: {app_count}")

            return True
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False

def create_sample_data():
    """Create sample data for testing"""
    print("🎯 Creating sample data...")

    try:
        with app.app_context():
            from app.models import User
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                print("❌ No admin user found. Cannot create sample data.")
                return False
            # Check if sample data already exists
            if App.query.count() > 0:
                print("ℹ️  Sample data already exists, skipping...")
                return True

            # Create sample apps
            sample_apps = [
                {
                    'name': 'PEPE Code Editor',
                    'description': 'A powerful text editor with syntax highlighting, multiple tabs, and advanced search features. Perfect for developers and writers who need a reliable tool for editing code and documents. Features PEPE-themed interface with green glow effects.',
                    'short_description': 'Professional text editor with PEPE theme',
                    'version': '2.1.0',
                    'developer': 'PEPE Dev Team',
                    'uploaded_by': 'PepeMaster',
                    'category': 'Development',
                    'price': 0.0,
                    'file_path': 'uploads/apps/sample_texteditor.zip',
                    'file_size': 15728640,  # 15MB
                    'is_featured': True
                },
                {
                    'name': 'PEPE Image Viewer',
                    'description': 'Fast and lightweight photo viewer supporting all major image formats. Features include slideshow mode, basic editing tools, and batch operations. Enhanced with PEPE green theme and smooth animations.',
                    'short_description': 'Fast photo viewer with PEPE styling',
                    'version': '1.5.2',
                    'developer': 'PEPE Graphics',
                    'uploaded_by': 'FrogLord',
                    'category': 'Graphics',
                    'price': 9.99,
                    'file_path': 'uploads/apps/sample_photoviewer.zip',
                    'file_size': 8388608,  # 8MB
                    'is_featured': False
                },
                {
                    'name': 'PEPE Task Master',
                    'description': 'Simple and effective task management application. Organize your daily tasks, set reminders, and track your productivity with this intuitive tool. Features the signature PEPE green interface with glow effects.',
                    'short_description': 'Task management with PEPE power',
                    'version': '3.0.1',
                    'developer': 'PEPE Productivity',
                    'uploaded_by': 'TaskFrog',
                    'category': 'Productivity',
                    'price': 4.99,
                    'file_path': 'uploads/apps/sample_taskmanager.zip',
                    'file_size': 5242880,  # 5MB
                    'is_featured': True
                }
            ]

            for app_data in sample_apps:
                # Create dummy file
                os.makedirs(os.path.dirname(app_data['file_path']), exist_ok=True)
                with open(app_data['file_path'], 'w') as f:
                    f.write(f"Sample app file for {app_data['name']}")
                app_data['user_id'] = admin_user.id  # Assign admin as uploader
                sample_app = App(**app_data)
                db.session.add(sample_app)

            db.session.commit()
            print("✅ Sample data created successfully")
            return True

    except Exception as e:
        print(f"❌ Failed to create sample data: {e}")
        return False

def check_system_requirements():
    """Check if system meets requirements"""
    print("🔍 Checking system requirements...")

    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print(f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    else:
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")

    # Check pip
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', '--version'],
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False

    return True

def set_admin_user():
    """Create or update the admin user in the database with the provided username and password."""
    from app.models import User
    from getpass import getpass
    with app.app_context():
        admin_username = os.environ.get('ADMIN_USERNAME') or input('Enter admin username [admin]: ') or 'admin'
        admin_email = os.environ.get('ADMIN_EMAIL') or input('Enter admin email [<EMAIL>]: ') or '<EMAIL>'
        admin_password = os.environ.get('ADMIN_PASSWORD')
        if not admin_password:
            admin_password = getpass('Enter admin password: ')
            confirm_password = getpass('Confirm admin password: ')
            if admin_password != confirm_password:
                print('Passwords do not match. Aborting admin setup.')
                return False
        if not admin_email or '@' not in admin_email:
            print('Invalid admin email. Aborting admin setup.')
            return False
        # Check for existing user by username or email
        user = User.query.filter((User.username == admin_username) | (User.email == admin_email)).first()
        if user:
            user.username = admin_username
            user.email = admin_email
            user.set_password(admin_password)
            user.role = 'admin'
            user.is_active = True
            print(f"Updated admin user: {admin_username} ({admin_email})")
        else:
            user = User(username=admin_username, email=admin_email, role='admin', is_active=True)
            user.set_password(admin_password)
            db.session.add(user)
            print(f"Created admin user: {admin_username} ({admin_email})")
        db.session.commit()
        # Ensure at least one admin exists
        admin_count = User.query.filter_by(role='admin').count()
        if admin_count == 0:
            print('Warning: No admin user exists!')
            return False
        return True

def main():
    """Main setup function"""
    print("🏪 APP STORE - Setup Script")
    print("=" * 50)

    # Backup databases before making changes
    print("💾 Backing up databases before setup...")
    try:
        import backup_db
        backup_db.backup_databases()
    except Exception as e:
        print(f"Warning: Could not backup databases: {e}")

    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please install Python 3.8+ and pip.")
        sys.exit(1)

    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies. Please check your internet connection and try again.")
        sys.exit(1)

    # Create directories
    create_directories()

    # Initialize database
    if not initialize_database():
        print("\n❌ Failed to initialize database.")
        sys.exit(1)

    # Set up admin user
    print("\n🔑 Setting up admin user...")
    if not set_admin_user():
        print('Admin user setup failed. Please check your input and try again.')
        sys.exit(1)

    # Ask user if they want sample data
    while True:
        choice = input("\n🎯 Would you like to create sample data for testing? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            create_sample_data()
            break
        elif choice in ['n', 'no']:
            print("ℹ️  Skipping sample data creation")
            break
        else:
            print("Please enter 'y' or 'n'")

    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Run the application: py -3.11 run.py")
    print("2. Open your browser to: http://localhost:5000")
    print("\nFor production deployment:")
    print("- Change admin credentials in config.py")
    print("- Set proper SECRET_KEY environment variable")
    print("- Use a production WSGI server like Gunicorn")
    print("=" * 50)

if __name__ == '__main__':
    main()
