# HTML-rendering views for main app, profiles, and publisher system

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort, make_response, Markup
from app.models import db, App, Screenshot, DownloadLog, AdminLog, LoginLog, ActivityLog, User, AppRating
import bleach, os, uuid, hmac, hashlib, sqlite3
from datetime import datetime
from urllib.parse import urlparse
import markdown

views = Blueprint('views', __name__)

@views.app_template_filter('markdown')
def markdown_filter(text):
    html = markdown.markdown(text or '', extensions=['fenced_code', 'codehilite'])
    return Markup(html)

@views.before_app_request
def check_cookie_auth():
    allow = ['/registerf', '/gate', '/static']
    if any(request.path.startswith(a) for a in allow):
        return
    uuid_token = request.cookies.get('uuid')
    fingerprint = request.cookies.get('fp')
    signature = request.cookies.get('sig')
    if not all([uuid_token, fingerprint, signature]):
        return redirect('/gate')
    with sqlite3.connect('app/fingerprints.db') as conn:
        cur = conn.cursor()
        cur.execute('SELECT hmac_key FROM fingerprints WHERE uuid=? AND fingerprint=?', (uuid_token, fingerprint))
        row = cur.fetchone()
    if not row:
        return redirect('/gate')
    hmac_key = row[0]
    expected_sig = hmac.new(hmac_key.encode(), f"{uuid_token}:{fingerprint}".encode(), hashlib.sha256).hexdigest()
    if not hmac.compare_digest(expected_sig, signature):
        return redirect('/gate')

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    import bleach
    return bleach.clean(text, tags=[], strip=True)

@views.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')
    query = App.query
    if category:
        query = query.filter(App.category == category)
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            db.or_(
                App.name.like(search_term),
                App.description.like(search_term),
                App.developer.like(search_term)
            )
        )
    apps = query.order_by(App.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    featured_apps = App.query.filter(App.is_featured == True).limit(6).all()
    categories = db.session.query(App.category).distinct().all()
    categories = [cat[0] for cat in categories]
    return render_template('index.html',
                         apps=apps,
                         featured_apps=featured_apps,
                         categories=categories,
                         current_category=category,
                         search_term=search)

@views.route('/gate')
def gate():
    return render_template('gate.html')

@views.route('/registerf', methods=['POST'])
def registerf():
    data = request.get_json()
    fingerprint = data['fingerprint']
    browser_data = str(data['browser_data'])
    with sqlite3.connect('app/fingerprints.db') as conn:
        cur = conn.cursor()
        cur.execute('SELECT uuid, hmac_key FROM fingerprints WHERE fingerprint=?', (fingerprint,))
        row = cur.fetchone()
        if row:
            user_uuid, hmac_key = row
        else:
            user_uuid = str(uuid.uuid4())
            hmac_key = uuid.uuid4().hex
            cur.execute('INSERT INTO fingerprints (uuid, fingerprint, hmac_key, browser_data) VALUES (?, ?, ?, ?)',
                        (user_uuid, fingerprint, hmac_key, browser_data))
            conn.commit()
    sig = hmac.new(hmac_key.encode(), f"{user_uuid}:{fingerprint}".encode(), hashlib.sha256).hexdigest()
    resp = make_response(jsonify({'success': True}))
    resp.set_cookie('uuid', user_uuid, httponly=True)
    resp.set_cookie('fp', fingerprint, httponly=True)
    resp.set_cookie('sig', sig, httponly=True)
    return resp

@views.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.is_active:
            user.last_login = datetime.utcnow()
            db.session.commit()
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            session.permanent = True
            # log_user_action logic here if needed
            flash(f'Welcome back, {user.username}!', 'success')
            if user.is_admin():
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('publisher_dashboard'))
        else:
            user_exists = User.query.filter_by(username=username).first()
            if not user_exists:
                reason = 'User not found'
            elif not user_exists.is_active:
                reason = 'Account disabled'
            else:
                reason = 'Invalid password'
            # log_failed_login logic here if needed
            flash('Invalid credentials', 'error')
    return render_template('auth/login.html')
